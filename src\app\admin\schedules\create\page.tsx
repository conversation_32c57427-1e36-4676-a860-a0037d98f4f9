'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Calendar } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import ScheduleForm from '@/components/schedule/ScheduleForm'

export default function CreateSchedulePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  const [selectedDate, setSelectedDate] = useState<string | undefined>(undefined)
  const [timeInfo, setTimeInfo] = useState<{ session?: string; timeSlot?: string } | undefined>(undefined)

  useEffect(() => {
    // Get date and time info from URL params
    const date = searchParams.get('date')
    const session = searchParams.get('session')
    const timeSlot = searchParams.get('timeSlot')

    if (date) {
      setSelectedDate(date)
    }

    if (session || timeSlot) {
      setTimeInfo({
        session: session || undefined,
        timeSlot: timeSlot || undefined
      })
    }
  }, [searchParams])

  const handleSave = async (formData: any) => {
    try {
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "Thành công",
        description: "Lịch giảng đã được tạo thành công"
      })

      router.push('/admin/schedules')
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tạo lịch giảng",
        variant: "destructive"
      })
      throw error
    }
  }

  const handleCancel = () => {
    router.push('/admin/schedules')
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/admin/schedules')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Quay lại
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Tạo lịch giảng mới
          </h1>
          <p className="text-muted-foreground">
            Sắp xếp lịch giảng chi tiết cho giảng viên và lớp học
          </p>
        </div>
      </div>

      <ScheduleForm
        selectedDate={selectedDate}
        timeInfo={timeInfo}
        onSave={handleSave}
        onCancel={handleCancel}
        isEdit={false}
      />
    </div>
  )
}
