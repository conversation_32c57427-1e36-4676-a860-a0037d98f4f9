'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, Save, Calendar } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface ScheduleFormData {
  subject: string
  class: string
  teacher: string
  room: string
  dayOfWeek: string
  startTime: string
  endTime: string
  startDate: string
  endDate: string
  notes: string
}

export default function CreateSchedulePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<ScheduleFormData>({
    subject: '',
    class: '',
    teacher: '',
    room: '',
    dayOfWeek: '',
    startTime: '',
    endTime: '',
    startDate: '',
    endDate: '',
    notes: ''
  })

  const handleInputChange = (field: keyof ScheduleFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!formData.subject || !formData.class || !formData.teacher || !formData.room || 
        !formData.dayOfWeek || !formData.startTime || !formData.endTime ||
        !formData.startDate || !formData.endDate) {
      toast({
        title: "Lỗi",
        description: "Vui lòng điền đầy đủ thông tin bắt buộc",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    
    try {
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Thành công",
        description: "Lịch giảng đã được tạo thành công"
      })
      
      router.push('/admin/schedules')
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tạo lịch giảng",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/admin/schedules')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Quay lại
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Tạo lịch giảng mới
          </h1>
          <p className="text-muted-foreground">
            Sắp xếp lịch giảng cho giảng viên và lớp học
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Thông tin cơ bản</CardTitle>
              <CardDescription>
                Thông tin về môn học và lớp học
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="subject">Môn học *</Label>
                <Select value={formData.subject} onValueChange={(value) => handleInputChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn môn học" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="lap-trinh-java">Lập trình Java</SelectItem>
                    <SelectItem value="co-so-du-lieu">Cơ sở dữ liệu</SelectItem>
                    <SelectItem value="mang-may-tinh">Mạng máy tính</SelectItem>
                    <SelectItem value="he-dieu-hanh">Hệ điều hành</SelectItem>
                    <SelectItem value="phan-tich-thiet-ke">Phân tích thiết kế hệ thống</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="class">Lớp học *</Label>
                <Select value={formData.class} onValueChange={(value) => handleInputChange('class', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn lớp học" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CNTT01">CNTT01</SelectItem>
                    <SelectItem value="CNTT02">CNTT02</SelectItem>
                    <SelectItem value="CNTT03">CNTT03</SelectItem>
                    <SelectItem value="CNTT04">CNTT04</SelectItem>
                    <SelectItem value="KTPM01">KTPM01</SelectItem>
                    <SelectItem value="KTPM02">KTPM02</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="teacher">Giảng viên *</Label>
                <Select value={formData.teacher} onValueChange={(value) => handleInputChange('teacher', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn giảng viên" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="nguyen-van-a">Nguyễn Văn A</SelectItem>
                    <SelectItem value="tran-thi-b">Trần Thị B</SelectItem>
                    <SelectItem value="le-van-c">Lê Văn C</SelectItem>
                    <SelectItem value="pham-thi-d">Phạm Thị D</SelectItem>
                    <SelectItem value="hoang-van-e">Hoàng Văn E</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="room">Phòng học *</Label>
                <Select value={formData.room} onValueChange={(value) => handleInputChange('room', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn phòng học" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="P101">P101</SelectItem>
                    <SelectItem value="P102">P102</SelectItem>
                    <SelectItem value="P103">P103</SelectItem>
                    <SelectItem value="P201">P201</SelectItem>
                    <SelectItem value="P202">P202</SelectItem>
                    <SelectItem value="LAB01">LAB01</SelectItem>
                    <SelectItem value="LAB02">LAB02</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Schedule Information */}
          <Card>
            <CardHeader>
              <CardTitle>Thời gian học</CardTitle>
              <CardDescription>
                Thông tin về thời gian và lịch học
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="dayOfWeek">Thứ trong tuần *</Label>
                <Select value={formData.dayOfWeek} onValueChange={(value) => handleInputChange('dayOfWeek', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn thứ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Thứ 2">Thứ 2</SelectItem>
                    <SelectItem value="Thứ 3">Thứ 3</SelectItem>
                    <SelectItem value="Thứ 4">Thứ 4</SelectItem>
                    <SelectItem value="Thứ 5">Thứ 5</SelectItem>
                    <SelectItem value="Thứ 6">Thứ 6</SelectItem>
                    <SelectItem value="Thứ 7">Thứ 7</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startTime">Giờ bắt đầu *</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={formData.startTime}
                    onChange={(e) => handleInputChange('startTime', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="endTime">Giờ kết thúc *</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={formData.endTime}
                    onChange={(e) => handleInputChange('endTime', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Ngày bắt đầu *</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">Ngày kết thúc *</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Ghi chú</Label>
                <Textarea
                  id="notes"
                  placeholder="Ghi chú thêm về lịch giảng..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/schedules')}
          >
            Hủy
          </Button>
          <Button type="submit" disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Đang tạo...' : 'Tạo lịch giảng'}
          </Button>
        </div>
      </form>
    </div>
  )
}
