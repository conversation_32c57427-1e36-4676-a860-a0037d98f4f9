import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'

// Interfaces for Master Data
export interface Department {
  id?: number
  maKhoa: string
  tenKhoa: string
  moTa?: string
  email?: string
  soDienThoai?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Subject {
  id?: number
  maMonHoc: string
  tenMonHoc: string
  soTinChi: number
  soTietLyThuyet: number
  soTietThucHanh: number
  moTa?: string
  idKhoa: number
  tenKhoa?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface ClassInfo {
  id?: number
  maLop: string
  tenLop: string
  siSo: number
  khoaHoc: string
  idKhoa: number
  tenKhoa?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Room {
  id?: number
  maPhong: string
  tenPhong: string
  sucChua: number
  loaiPhong: string
  trangThietBi?: string
  idCoSo: number
  tenCoSo?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Teacher {
  id?: number
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai?: string
  chuyenMon?: string
  hocVi?: string
  idKhoa: number
  tenKhoa?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Campus {
  id?: number
  maCoSo: string
  tenCoSo: string
  diaChi: string
  soDienThoai?: string
  email?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface PaginatedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

class MasterDataService {
  // Department Management
  async getDepartments(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Department>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS, params)
    return response.data
  }

  async getDepartmentById(id: number): Promise<Department> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/${id}`)
    return response.data
  }

  async createDepartment(department: Omit<Department, 'id'>): Promise<Department> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS, department)
    return response.data
  }

  async updateDepartment(id: number, department: Partial<Department>): Promise<Department> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/${id}`, department)
    return response.data
  }

  async deleteDepartment(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/${id}`)
  }

  // Subject Management
  async getSubjects(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Subject>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS, params)
    return response.data
  }

  async createSubject(subject: Omit<Subject, 'id'>): Promise<Subject> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS, subject)
    return response.data
  }

  async updateSubject(id: number, subject: Partial<Subject>): Promise<Subject> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS}/${id}`, subject)
    return response.data
  }

  async deleteSubject(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS}/${id}`)
  }

  // Class Management
  async getClasses(page = 0, size = 10, search = ''): Promise<PaginatedResponse<ClassInfo>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES, params)
    return response.data
  }

  async createClass(classInfo: Omit<ClassInfo, 'id'>): Promise<ClassInfo> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES, classInfo)
    return response.data
  }

  async updateClass(id: number, classInfo: Partial<ClassInfo>): Promise<ClassInfo> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES}/${id}`, classInfo)
    return response.data
  }

  async deleteClass(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES}/${id}`)
  }

  // Room Management
  async getRooms(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Room>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS, params)
    return response.data
  }

  async createRoom(room: Omit<Room, 'id'>): Promise<Room> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS, room)
    return response.data
  }

  async updateRoom(id: number, room: Partial<Room>): Promise<Room> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS}/${id}`, room)
    return response.data
  }

  async deleteRoom(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS}/${id}`)
  }

  // Teacher Management
  async getTeachers(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Teacher>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS, params)
    return response.data
  }

  async createTeacher(teacher: Omit<Teacher, 'id'>): Promise<Teacher> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS, teacher)
    return response.data
  }

  async updateTeacher(id: number, teacher: Partial<Teacher>): Promise<Teacher> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS}/${id}`, teacher)
    return response.data
  }

  async deleteTeacher(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS}/${id}`)
  }

  // Campus Management
  async getCampuses(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Campus>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES, params)
    return response.data
  }

  async createCampus(campus: Omit<Campus, 'id'>): Promise<Campus> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES, campus)
    return response.data
  }

  async updateCampus(id: number, campus: Partial<Campus>): Promise<Campus> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES}/${id}`, campus)
    return response.data
  }

  async deleteCampus(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES}/${id}`)
  }

  // Utility methods
  async getAllDepartments(): Promise<Department[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/all`)
    return response.data
  }

  async getAllCampuses(): Promise<Campus[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES}/all`)
    return response.data
  }

  // Get statistics
  async getMasterDataStats(): Promise<{
    departments: number
    subjects: number
    classes: number
    rooms: number
    teachers: number
    campuses: number
  }> {
    try {
      const response = await api.get('/api/master-data/stats')
      return response.data
    } catch (error) {
      // Fallback to mock data if API is not available
      console.warn('Master data stats API not available, using mock data')
      return {
        departments: 12,
        subjects: 245,
        classes: 89,
        rooms: 67,
        teachers: 156,
        campuses: 3
      }
    }
  }
}

export const masterDataService = new MasterDataService()
export default masterDataService
