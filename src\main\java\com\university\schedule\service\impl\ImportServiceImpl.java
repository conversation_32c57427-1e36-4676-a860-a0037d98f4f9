package com.university.schedule.service.impl;

import com.university.schedule.entity.*;
import com.university.schedule.repository.*;
import com.university.schedule.service.ImportService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class ImportServiceImpl implements ImportService {

    @Autowired
    private MonHocRepository monHocRepository;
    
    @Autowired
    private LopRepository lopRepository;
    
    @Autowired
    private CanBoRepository canBoRepository;
    
    @Autowired
    private PhongRepository phongRepository;
    
    @Autowired
    private NamHocRepository namHocRepository;

    @Override
    public Map<String, Object> syncDataFromSource(String type) throws Exception {
        Map<String, Object> result = new HashMap<>();
        
        switch (type.toLowerCase()) {
            case "subjects":
                result = syncSubjects();
                break;
            case "classes":
                result = syncClasses();
                break;
            case "teachers":
                result = syncTeachers();
                break;
            case "rooms":
                result = syncRooms();
                break;
            case "academic_years":
                result = syncAcademicYears();
                break;
            default:
                throw new Exception("Loại dữ liệu không được hỗ trợ: " + type);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> importFromFile(MultipartFile file, String type) throws Exception {
        Map<String, Object> result = new HashMap<>();
        String fileName = file.getOriginalFilename();
        
        if (fileName == null) {
            throw new Exception("Tên file không hợp lệ");
        }
        
        if (fileName.endsWith(".csv")) {
            result = importFromCSV(file, type);
        } else if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
            result = importFromExcel(file, type);
        } else {
            throw new Exception("Định dạng file không được hỗ trợ. Chỉ hỗ trợ CSV và Excel");
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getImportStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // Đếm số lượng bản ghi của từng loại
        status.put("subjects", Map.of(
            "count", monHocRepository.count(),
            "lastSync", "2024-01-15 09:30:00",
            "status", "success"
        ));
        
        status.put("classes", Map.of(
            "count", lopRepository.count(),
            "lastSync", "2024-01-15 09:30:00",
            "status", "success"
        ));
        
        status.put("teachers", Map.of(
            "count", canBoRepository.count(),
            "lastSync", "2024-01-15 09:30:00",
            "status", "success"
        ));
        
        status.put("rooms", Map.of(
            "count", phongRepository.count(),
            "lastSync", "2024-01-14 14:20:00",
            "status", "success"
        ));
        
        status.put("academic_years", Map.of(
            "count", namHocRepository.count(),
            "lastSync", "2024-01-10 10:00:00",
            "status", "success"
        ));
        
        return status;
    }

    @Override
    public void clearData(String type) throws Exception {
        switch (type.toLowerCase()) {
            case "subjects":
                monHocRepository.deleteAll();
                break;
            case "classes":
                lopRepository.deleteAll();
                break;
            case "teachers":
                canBoRepository.deleteAll();
                break;
            case "rooms":
                phongRepository.deleteAll();
                break;
            case "academic_years":
                namHocRepository.deleteAll();
                break;
            default:
                throw new Exception("Loại dữ liệu không được hỗ trợ: " + type);
        }
    }

    @Override
    public boolean testSourceConnection() {
        try {
            // Simulate connection test to external system
            // In real implementation, this would test connection to actual source system
            Thread.sleep(1000); // Simulate network delay
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // Private helper methods
    private Map<String, Object> syncSubjects() {
        // Simulate syncing subjects from external system
        List<MonHoc> subjects = Arrays.asList(
            createMonHoc("IT001", "Lập trình Java", 3, "Bắt buộc"),
            createMonHoc("IT002", "Cơ sở dữ liệu", 3, "Bắt buộc"),
            createMonHoc("IT003", "Mạng máy tính", 3, "Bắt buộc"),
            createMonHoc("IT004", "Phân tích thiết kế hệ thống", 3, "Tự chọn"),
            createMonHoc("IT005", "Lập trình Web", 3, "Tự chọn")
        );
        
        monHocRepository.saveAll(subjects);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", subjects.size());
        result.put("type", "subjects");
        result.put("message", "Đồng bộ môn học thành công");
        
        return result;
    }

    private Map<String, Object> syncClasses() {
        // Simulate syncing classes from external system
        List<Lop> classes = Arrays.asList(
            createLop("CNTT01", "Công nghệ thông tin 01", "CNTT", "2023", 35),
            createLop("CNTT02", "Công nghệ thông tin 02", "CNTT", "2023", 32),
            createLop("CNTT03", "Công nghệ thông tin 03", "CNTT", "2023", 30),
            createLop("KTPM01", "Kỹ thuật phần mềm 01", "CNTT", "2023", 28)
        );
        
        lopRepository.saveAll(classes);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", classes.size());
        result.put("type", "classes");
        result.put("message", "Đồng bộ lớp học thành công");
        
        return result;
    }

    private Map<String, Object> syncTeachers() {
        // Simulate syncing teachers from external system
        List<CanBo> teachers = Arrays.asList(
            createCanBo("GV001", "Nguyễn Văn A", "<EMAIL>", "0123456789", "CNTT", "Giảng viên"),
            createCanBo("GV002", "Trần Thị B", "<EMAIL>", "0987654321", "CNTT", "Phó giáo sư"),
            createCanBo("GV003", "Lê Văn C", "<EMAIL>", "0111222333", "CNTT", "Giảng viên"),
            createCanBo("GV004", "Phạm Thị D", "<EMAIL>", "0444555666", "CNTT", "Tiến sĩ")
        );
        
        canBoRepository.saveAll(teachers);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", teachers.size());
        result.put("type", "teachers");
        result.put("message", "Đồng bộ giảng viên thành công");
        
        return result;
    }

    private Map<String, Object> syncRooms() {
        // Simulate syncing rooms from external system
        List<Phong> rooms = Arrays.asList(
            createPhong("A101", "Phòng A101", "Lý thuyết", 50, "Cơ sở 1"),
            createPhong("A102", "Phòng A102", "Lý thuyết", 45, "Cơ sở 1"),
            createPhong("B201", "Phòng B201", "Thực hành", 30, "Cơ sở 1"),
            createPhong("B202", "Phòng B202", "Thực hành", 25, "Cơ sở 1"),
            createPhong("C301", "Phòng C301", "Hội thảo", 100, "Cơ sở 2")
        );
        
        phongRepository.saveAll(rooms);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", rooms.size());
        result.put("type", "rooms");
        result.put("message", "Đồng bộ phòng học thành công");
        
        return result;
    }

    private Map<String, Object> syncAcademicYears() {
        // Simulate syncing academic years from external system
        List<NamHoc> academicYears = Arrays.asList(
            createNamHoc("2023-2024", 1, LocalDate.of(2023, 9, 1), LocalDate.of(2024, 1, 15), "Đã kết thúc"),
            createNamHoc("2023-2024", 2, LocalDate.of(2024, 1, 16), LocalDate.of(2024, 5, 30), "Đang diễn ra"),
            createNamHoc("2024-2025", 1, LocalDate.of(2024, 9, 1), LocalDate.of(2025, 1, 15), "Sắp diễn ra")
        );
        
        namHocRepository.saveAll(academicYears);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", academicYears.size());
        result.put("type", "academic_years");
        result.put("message", "Đồng bộ năm học thành công");
        
        return result;
    }

    private Map<String, Object> importFromCSV(MultipartFile file, String type) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()));
        String line;
        List<String[]> data = new ArrayList<>();
        
        // Skip header
        reader.readLine();
        
        while ((line = reader.readLine()) != null) {
            data.add(line.split(","));
        }
        
        return processImportData(data, type);
    }

    private Map<String, Object> importFromExcel(MultipartFile file, String type) throws Exception {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        List<String[]> data = new ArrayList<>();
        
        // Skip header row
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                String[] rowData = new String[row.getLastCellNum()];
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    rowData[j] = cell != null ? cell.toString() : "";
                }
                data.add(rowData);
            }
        }
        
        workbook.close();
        return processImportData(data, type);
    }

    private Map<String, Object> processImportData(List<String[]> data, String type) throws Exception {
        int count = 0;
        
        switch (type.toLowerCase()) {
            case "subjects":
                for (String[] row : data) {
                    if (row.length >= 4) {
                        MonHoc monHoc = createMonHoc(row[0], row[1], 
                            Integer.parseInt(row[2]), row[3]);
                        monHocRepository.save(monHoc);
                        count++;
                    }
                }
                break;
            case "classes":
                for (String[] row : data) {
                    if (row.length >= 5) {
                        Lop lop = createLop(row[0], row[1], row[2], row[3], 
                            Integer.parseInt(row[4]));
                        lopRepository.save(lop);
                        count++;
                    }
                }
                break;
            // Add other cases as needed
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        result.put("type", type);
        result.put("message", "Import thành công " + count + " bản ghi");
        
        return result;
    }

    // Helper methods to create entities
    private MonHoc createMonHoc(String ma, String ten, int soTinChi, String loai) {
        MonHoc monHoc = new MonHoc();
        monHoc.setMaMonHoc(ma);
        monHoc.setTenMonHoc(ten);
        monHoc.setSoTinChi(soTinChi);
        monHoc.setLoaiMon(loai);
        return monHoc;
    }

    private Lop createLop(String ma, String ten, String khoa, String khoaHoc, int siSo) {
        Lop lop = new Lop();
        lop.setMaLop(ma);
        lop.setTenLop(ten);
        lop.setKhoa(khoa);
        lop.setKhoaHoc(khoaHoc);
        lop.setSiSo(siSo);
        return lop;
    }

    private CanBo createCanBo(String ma, String ten, String email, String sdt, String khoa, String chucVu) {
        CanBo canBo = new CanBo();
        canBo.setMaCanBo(ma);
        canBo.setTenCanBo(ten);
        canBo.setEmail(email);
        canBo.setSoDienThoai(sdt);
        canBo.setKhoa(khoa);
        canBo.setChucVu(chucVu);
        canBo.setMatKhau("123456"); // Default password
        canBo.setVaiTro("GIANG_VIEN");
        return canBo;
    }

    private Phong createPhong(String ma, String ten, String loai, int sucChua, String coSo) {
        Phong phong = new Phong();
        phong.setMaPhong(ma);
        phong.setTenPhong(ten);
        phong.setLoaiPhong(loai);
        phong.setSucChua(sucChua);
        phong.setCoSo(coSo);
        return phong;
    }

    private NamHoc createNamHoc(String namHoc, int hocKy, LocalDate ngayBatDau, LocalDate ngayKetThuc, String trangThai) {
        NamHoc nh = new NamHoc();
        nh.setNamHoc(namHoc);
        nh.setHocKy(hocKy);
        nh.setNgayBatDau(ngayBatDau);
        nh.setNgayKetThuc(ngayKetThuc);
        nh.setTrangThai(trangThai);
        return nh;
    }
}
