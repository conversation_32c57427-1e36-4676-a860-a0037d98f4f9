'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Calendar,
  Clock,
  ArrowLeft,
  Filter,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  MapPin,
  User,
  Grid,
  CalendarDays
} from 'lucide-react'
import ScheduleCalendar from '@/components/calendar/ScheduleCalendar'
import ScheduleDetailModal from '@/components/calendar/ScheduleDetailModal'
import QuickCreateModal from '@/components/calendar/QuickCreateModal'

interface Schedule {
  id: number
  subject: string
  class: string
  teacher: string
  room: string
  dayOfWeek: string
  startTime: string
  endTime: string
  startDate: string
  endDate: string
  status: 'active' | 'pending' | 'cancelled'
}

export default function AdminSchedulesPage() {
  const router = useRouter()
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isQuickCreateOpen, setIsQuickCreateOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<string | null>(null)
  const [selectedTimeInfo, setSelectedTimeInfo] = useState<{ session?: string; timeSlot?: string } | undefined>(undefined)
  const [activeTab, setActiveTab] = useState('calendar')

  // Mock data
  useEffect(() => {
    const mockSchedules: Schedule[] = [
      {
        id: 1,
        subject: 'Lập trình Java',
        class: 'CNTT01',
        teacher: 'Nguyễn Văn A',
        room: 'P101',
        dayOfWeek: 'Thứ 2',
        startTime: '07:00',
        endTime: '09:00',
        startDate: '2024-01-15',
        endDate: '2024-05-15',
        status: 'active'
      },
      {
        id: 2,
        subject: 'Cơ sở dữ liệu',
        class: 'CNTT02',
        teacher: 'Trần Thị B',
        room: 'P102',
        dayOfWeek: 'Thứ 3',
        startTime: '09:00',
        endTime: '11:00',
        startDate: '2024-01-16',
        endDate: '2024-05-16',
        status: 'active'
      },
      {
        id: 3,
        subject: 'Mạng máy tính',
        class: 'CNTT03',
        teacher: 'Lê Văn C',
        room: 'P103',
        dayOfWeek: 'Thứ 4',
        startTime: '13:00',
        endTime: '15:00',
        startDate: '2024-01-17',
        endDate: '2024-05-17',
        status: 'pending'
      }
    ]
    setSchedules(mockSchedules)
  }, [])

  const filteredSchedules = schedules.filter(schedule => {
    const matchesSearch = schedule.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         schedule.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         schedule.teacher.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         schedule.room.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || schedule.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">Đang hoạt động</Badge>
      case 'pending':
        return <Badge variant="secondary">Chờ duyệt</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  const handleEdit = (id: number) => {
    router.push(`/admin/schedules/edit/${id}`)
  }

  const handleDelete = (id: number) => {
    if (confirm('Bạn có chắc chắn muốn xóa lịch giảng này?')) {
      setSchedules(schedules.filter(s => s.id !== id))
    }
  }

  const handleView = (id: number) => {
    const schedule = schedules.find(s => s.id === id)
    if (schedule) {
      setSelectedSchedule(schedule)
      setIsDetailModalOpen(true)
    }
  }

  const handleScheduleClick = (schedule: Schedule) => {
    setSelectedSchedule(schedule)
    setIsDetailModalOpen(true)
  }

  const handleDateClick = (date: string, timeInfo?: { session?: string; timeSlot?: string }) => {
    setSelectedDate(date)
    setSelectedTimeInfo(timeInfo)
    setIsQuickCreateOpen(true)
  }

  const handleCreateSchedule = (date: string, timeInfo?: { session?: string; timeSlot?: string }) => {
    setSelectedDate(date)
    setSelectedTimeInfo(timeInfo)
    setIsQuickCreateOpen(true)
  }

  const handleQuickSave = async (scheduleData: any) => {
    // Mock save - replace with actual API call
    const newSchedule: Schedule = {
      id: schedules.length + 1,
      subject: scheduleData.subject,
      class: scheduleData.class,
      teacher: scheduleData.teacher,
      room: scheduleData.room,
      dayOfWeek: new Date(scheduleData.date).toLocaleDateString('vi-VN', { weekday: 'long' }),
      startTime: scheduleData.startTime,
      endTime: scheduleData.endTime,
      startDate: scheduleData.date,
      endDate: scheduleData.date,
      status: 'pending'
    }

    setSchedules(prev => [...prev, newSchedule])
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/admin')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Quản lý lịch giảng
            </h1>
            <p className="text-muted-foreground">
              Sắp xếp và quản lý lịch giảng cho toàn trường
            </p>
          </div>
        </div>
        <Button onClick={() => router.push('/admin/schedules/create')}>
          <Plus className="h-4 w-4 mr-2" />
          Tạo lịch mới
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
          <TabsTrigger value="calendar" className="flex items-center">
            <CalendarDays className="h-4 w-4 mr-2" />
            Lịch
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center">
            <Grid className="h-4 w-4 mr-2" />
            Danh sách
          </TabsTrigger>
        </TabsList>

        {/* Calendar View */}
        <TabsContent value="calendar" className="space-y-6">
          <ScheduleCalendar
            onScheduleClick={handleScheduleClick}
            onDateClick={handleDateClick}
            onCreateSchedule={handleCreateSchedule}
            isAdmin={true}
          />
        </TabsContent>

        {/* List View */}
        <TabsContent value="list" className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm kiếm theo môn học, lớp, giảng viên, phòng..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[200px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Lọc theo trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="active">Đang hoạt động</SelectItem>
                <SelectItem value="pending">Chờ duyệt</SelectItem>
                <SelectItem value="cancelled">Đã hủy</SelectItem>
              </SelectContent>
            </Select>
          </div>

      {/* Schedules Grid */}
      {filteredSchedules.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredSchedules.map((schedule) => (
            <Card key={schedule.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{schedule.subject}</CardTitle>
                    <CardDescription>Lớp {schedule.class}</CardDescription>
                  </div>
                  {getStatusBadge(schedule.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <User className="mr-2 h-4 w-4" />
                  {schedule.teacher}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="mr-2 h-4 w-4" />
                  Phòng {schedule.room}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="mr-2 h-4 w-4" />
                  {schedule.dayOfWeek}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="mr-2 h-4 w-4" />
                  {schedule.startTime} - {schedule.endTime}
                </div>

                <div className="flex space-x-2 pt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleView(schedule.id)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Xem
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(schedule.id)}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Sửa
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(schedule.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Xóa
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Calendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            Không có lịch giảng
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Không tìm thấy lịch giảng phù hợp.' : 'Bắt đầu bằng cách tạo lịch giảng mới.'}
          </p>
          {!searchTerm && (
            <div className="mt-6">
              <Button onClick={() => router.push('/admin/schedules/create')}>
                <Plus className="h-4 w-4 mr-2" />
                Tạo lịch giảng đầu tiên
              </Button>
            </div>
          )}
        </div>
      )}
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <ScheduleDetailModal
        schedule={selectedSchedule}
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        onEdit={(schedule) => router.push(`/admin/schedules/edit/${schedule.id}`)}
        onDelete={(id) => handleDelete(id)}
        onDuplicate={(schedule) => {
          // Handle duplicate logic
          console.log('Duplicate schedule:', schedule)
        }}
        isAdmin={true}
      />

      <QuickCreateModal
        isOpen={isQuickCreateOpen}
        onClose={() => setIsQuickCreateOpen(false)}
        selectedDate={selectedDate}
        timeInfo={selectedTimeInfo}
        onSave={handleQuickSave}
      />
    </div>
  )
}
