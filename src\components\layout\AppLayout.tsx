"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  GraduationCap, 
  LogOut, 
  User,
  Shield,
  Menu,
  X
} from "lucide-react"

interface UserInfo {
  id: number
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai: string
  vaiTro: string
  tenKhoa: string
  nu: boolean
}

interface AppLayoutProps {
  children: React.ReactNode
}

export default function AppLayout({ children }: AppLayoutProps) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  // Kiểm tra xem có phải trang public không
  const isPublicPage = pathname === '/login' || pathname === '/'
  
  // <PERSON><PERSON><PERSON> role dựa trên pathname
  const isAdminPage = pathname.startsWith('/admin')
  const isTeacherPage = pathname.startsWith('/teacher')

  useEffect(() => {
    // Nếu là trang public, không cần kiểm tra auth
    if (isPublicPage) {
      setLoading(false)
      return
    }

    // Kiểm tra token và quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Lấy thông tin user từ localStorage
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr && userInfoStr !== 'undefined') {
      try {
        const user = JSON.parse(userInfoStr)
        setUserInfo(user)
        
        // Kiểm tra quyền truy cập
        const isAdmin = user.vaiTro === 'Admin' || user.vaiTro === 'ADMIN' || user.vaiTro === 'QUAN_TRI'
        
        if (isAdminPage && !isAdmin) {
          router.push('/teacher')
          return
        }
        
        if (isTeacherPage && isAdmin) {
          router.push('/admin')
          return
        }
        
      } catch (error) {
        console.error('Error parsing user info:', error)
        localStorage.removeItem('userInfo')
        localStorage.removeItem('token')
        router.push('/login')
        return
      }
    } else {
      router.push('/login')
      return
    }
    
    setLoading(false)
  }, [router, pathname, isPublicPage, isAdminPage, isTeacherPage])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    // Xóa cookies
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    document.cookie = 'userInfo=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    router.push('/login')
  }

  const getRoleInfo = () => {
    if (!userInfo) return { icon: User, color: 'text-gray-600', badge: 'User', badgeColor: 'bg-gray-100' }
    
    const isAdmin = userInfo.vaiTro === 'Admin' || userInfo.vaiTro === 'ADMIN' || userInfo.vaiTro === 'QUAN_TRI'
    
    if (isAdmin) {
      return {
        icon: Shield,
        color: 'text-blue-600',
        badge: 'Quản trị viên',
        badgeColor: 'bg-blue-600 text-white'
      }
    } else {
      return {
        icon: User,
        color: 'text-green-600',
        badge: 'Giảng viên',
        badgeColor: 'bg-green-100 text-green-800'
      }
    }
  }

  const getPageTitle = () => {
    if (isAdminPage) return 'Bảng điều khiển quản trị'
    if (isTeacherPage) return 'Dành cho giảng viên'
    return 'Hệ thống quản lý lịch giảng'
  }

  // Nếu đang loading hoặc là trang public, hiển thị children trực tiếp
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (isPublicPage) {
    return <>{children}</>
  }

  if (!userInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const roleInfo = getRoleInfo()
  const RoleIcon = roleInfo.icon

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo và Title */}
            <div className="flex items-center">
              <GraduationCap className={`h-8 w-8 ${roleInfo.color} mr-3`} />
              <div className="hidden sm:block">
                <h1 className="text-xl font-semibold text-gray-900">
                  Hệ thống quản lý lịch giảng
                </h1>
                <p className="text-xs text-gray-500">
                  {getPageTitle()}
                </p>
              </div>
              <div className="sm:hidden">
                <h1 className="text-lg font-semibold text-gray-900">
                  QLGD
                </h1>
              </div>
            </div>

            {/* User Info và Actions */}
            <div className="flex items-center space-x-4">
              {/* Desktop User Info */}
              <div className="hidden md:flex items-center space-x-2">
                <RoleIcon className={`h-4 w-4 ${roleInfo.color}`} />
                <span className="text-sm text-gray-700">
                  <strong>{userInfo.tenCanBo}</strong>
                </span>
                <Badge className={roleInfo.badgeColor}>
                  {roleInfo.badge}
                </Badge>
              </div>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? (
                  <X className="h-4 w-4" />
                ) : (
                  <Menu className="h-4 w-4" />
                )}
              </Button>

              {/* Desktop Logout */}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleLogout}
                className="hidden md:flex"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Đăng xuất
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden border-t bg-white py-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2 px-2">
                  <RoleIcon className={`h-4 w-4 ${roleInfo.color}`} />
                  <span className="text-sm text-gray-700">
                    <strong>{userInfo.tenCanBo}</strong>
                  </span>
                  <Badge className={roleInfo.badgeColor}>
                    {roleInfo.badge}
                  </Badge>
                </div>
                <div className="px-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleLogout}
                    className="w-full"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Đăng xuất
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
}
