'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'

interface Schedule {
  id: number
  subject: string
  class: string
  teacher: string
  room: string
  startTime: string
  endTime: string
  date: string
  status: 'confirmed' | 'pending' | 'cancelled'
  color: string
}

interface WeekViewProps {
  currentDate: Date
  schedules: Schedule[]
  onScheduleClick?: (schedule: Schedule) => void
  onDateClick?: (date: string, timeInfo?: { session?: string; timeSlot?: string }) => void
  onCreateSchedule?: (date: string, timeInfo?: { session?: string; timeSlot?: string }) => void
  isAdmin?: boolean
}

export default function WeekView({
  currentDate,
  schedules,
  onScheduleClick,
  onDateClick,
  onCreateSchedule,
  isAdmin = false
}: WeekViewProps) {
  const getDaysInWeek = (date: Date) => {
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day
    startOfWeek.setDate(diff)

    const days = []
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(startOfWeek)
      currentDay.setDate(startOfWeek.getDate() + i)
      days.push(currentDay)
    }

    return days
  }

  const getSchedulesForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    return schedules.filter(schedule => schedule.date === dateString)
  }

  const timeSlots = [
    '07:00', '08:00', '09:00', '10:00', '11:00', '12:00',
    '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'
  ]

  const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7']
  const weekDays = getDaysInWeek(currentDate)

  const getScheduleForTimeSlot = (date: Date, timeSlot: string) => {
    const daySchedules = getSchedulesForDate(date)
    return daySchedules.find(schedule => {
      const scheduleHour = parseInt(schedule.startTime.split(':')[0])
      const slotHour = parseInt(timeSlot.split(':')[0])
      return scheduleHour === slotHour
    })
  }

  const isToday = (date: Date) => {
    return date.toDateString() === new Date().toDateString()
  }

  const getSessionFromTime = (timeSlot: string) => {
    const hour = parseInt(timeSlot.split(':')[0])
    if (hour >= 6 && hour < 12) return 'morning'
    if (hour >= 12 && hour < 18) return 'afternoon'
    if (hour >= 18 && hour <= 23) return 'evening'
    return 'morning'
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="grid grid-cols-8 border-b">
          {/* Time column header */}
          <div className="p-4 border-r bg-gray-50 font-medium text-center">
            Giờ
          </div>

          {/* Day headers */}
          {weekDays.map((date, index) => (
            <div
              key={index}
              className={`p-4 border-r text-center ${
                isToday(date) ? 'bg-blue-50 text-blue-600 font-bold' : 'bg-gray-50'
              }`}
            >
              <div className="text-sm font-medium">{dayNames[date.getDay()]}</div>
              <div className={`text-lg ${isToday(date) ? 'font-bold' : ''}`}>
                {date.getDate()}/{date.getMonth() + 1}
              </div>
            </div>
          ))}
        </div>

        {/* Time slots */}
        {timeSlots.map((timeSlot, timeIndex) => (
          <div key={timeSlot} className="grid grid-cols-8 border-b min-h-[80px]">
            {/* Time label */}
            <div className="p-2 border-r bg-gray-50 text-center text-sm font-medium flex items-center justify-center">
              {timeSlot}
            </div>

            {/* Day cells */}
            {weekDays.map((date, dayIndex) => {
              const schedule = getScheduleForTimeSlot(date, timeSlot)

              return (
                <div
                  key={`${timeSlot}-${dayIndex}`}
                  className={`border-r p-1 relative group cursor-pointer hover:bg-gray-50 ${
                    isToday(date) ? 'bg-blue-25' : ''
                  }`}
                  onClick={() => {
                    if (schedule) {
                      onScheduleClick?.(schedule)
                    } else {
                      const timeInfo = {
                        timeSlot: timeSlot,
                        session: getSessionFromTime(timeSlot)
                      }
                      console.log('WeekView click:', {
                        date: date.toISOString().split('T')[0],
                        timeInfo
                      })
                      onDateClick?.(date.toISOString().split('T')[0], timeInfo)
                    }
                  }}
                >
                  {schedule ? (
                    <div
                      className={`${schedule.color} text-white p-2 rounded text-xs h-full flex flex-col justify-center hover:opacity-80 transition-opacity`}
                      onClick={(e) => {
                        e.stopPropagation()
                        onScheduleClick?.(schedule)
                      }}
                    >
                      <div className="font-medium truncate">{schedule.subject}</div>
                      <div className="truncate">{schedule.class}</div>
                      <div className="truncate">{schedule.room}</div>
                      <div className="truncate">{schedule.startTime} - {schedule.endTime}</div>
                    </div>
                  ) : (
                    isAdmin && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="w-full h-full opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={(e) => {
                          e.stopPropagation()
                          const timeInfo = {
                            timeSlot: timeSlot,
                            session: getSessionFromTime(timeSlot)
                          }
                          onCreateSchedule?.(date.toISOString().split('T')[0], timeInfo)
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    )
                  )}
                </div>
              )
            })}
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
