package com.university.schedule.controller;

import com.university.schedule.dto.ApiResponse;
import com.university.schedule.service.ImportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
@RequestMapping("/api/import")
@CrossOrigin(origins = "*")
public class ImportController {

    @Autowired
    private ImportService importService;

    /**
     * Đồng bộ dữ liệu từ hệ thống nguồn
     */
    @PostMapping("/sync/{type}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> syncData(@PathVariable String type) {
        try {
            Map<String, Object> result = importService.syncDataFromSource(type);
            return ResponseEntity.ok(ApiResponse.success("Đồng bộ dữ liệu thành công", result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi đồng bộ dữ liệu: " + e.getMessage()));
        }
    }

    /**
     * Import dữ liệu từ file upload
     */
    @PostMapping("/upload")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("type") String type) {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("File không được để trống"));
            }

            Map<String, Object> result = importService.importFromFile(file, type);
            return ResponseEntity.ok(ApiResponse.success("Import file thành công", result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi import file: " + e.getMessage()));
        }
    }

    /**
     * Lấy trạng thái import của tất cả loại dữ liệu
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getImportStatus() {
        try {
            Map<String, Object> status = importService.getImportStatus();
            return ResponseEntity.ok(ApiResponse.success("Lấy trạng thái thành công", status));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi lấy trạng thái: " + e.getMessage()));
        }
    }

    /**
     * Xóa tất cả dữ liệu của một loại
     */
    @DeleteMapping("/clear/{type}")
    public ResponseEntity<ApiResponse<String>> clearData(@PathVariable String type) {
        try {
            importService.clearData(type);
            return ResponseEntity.ok(ApiResponse.success("Xóa dữ liệu thành công", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi xóa dữ liệu: " + e.getMessage()));
        }
    }

    /**
     * Kiểm tra kết nối với hệ thống nguồn
     */
    @GetMapping("/test-connection")
    public ResponseEntity<ApiResponse<String>> testConnection() {
        try {
            boolean isConnected = importService.testSourceConnection();
            if (isConnected) {
                return ResponseEntity.ok(ApiResponse.success("Kết nối thành công", "OK"));
            } else {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Không thể kết nối đến hệ thống nguồn"));
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi kiểm tra kết nối: " + e.getMessage()));
        }
    }
}
