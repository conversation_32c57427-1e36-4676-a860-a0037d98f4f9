'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import {
  GraduationCap,
  Menu,
  LogOut,
  Settings,
  User,
  Home,
  Calendar,
  Clock,
  FileText,
  Database,
  Building,
  BookOpen,
  Users,
  BarChart3
} from 'lucide-react'

interface UserInfo {
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai: string
  vaiTro: string
  tenKhoa?: string
}

interface NavigationItem {
  title: string
  href: string
  icon: any
  adminOnly?: boolean
  teacherOnly?: boolean
}

const navigation: NavigationItem[] = [
  { title: 'Dashboard', href: '/admin', icon: Home, adminOnly: true },
  { title: 'Dashboard', href: '/teacher', icon: Home, teacherOnly: true },
  { title: 'Năm học & Học kỳ', href: '/admin/academic-years', icon: Calendar, adminOnly: true },
  { title: 'Dữ liệu Danh mục', href: '/admin/master-data', icon: Database, adminOnly: true },
  { title: 'Import & Đồng bộ', href: '/admin/import-management', icon: FileText, adminOnly: true },
  { title: 'Thống kê & Báo cáo', href: '/admin/reports', icon: BarChart3, adminOnly: true },
  { title: 'Lịch giảng', href: '/teacher/schedules', icon: Calendar, teacherOnly: true },
  { title: 'Giờ giảng dạy', href: '/teacher/teaching-hours', icon: Clock, teacherOnly: true },
  { title: 'Báo cáo', href: '/teacher/reports', icon: FileText, teacherOnly: true },
  { title: 'Cài đặt', href: '/teacher/settings', icon: Settings, teacherOnly: true },
]

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr && userInfoStr !== 'undefined') {
      try {
        const user = JSON.parse(userInfoStr)
        setUserInfo(user)
      } catch (error) {
        console.error('Error parsing user info:', error)
        router.push('/login')
      }
    } else {
      router.push('/login')
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
  }

  const isAdmin = userInfo?.vaiTro === 'Admin' || userInfo?.vaiTro === 'ADMIN' || userInfo?.vaiTro === 'QUAN_TRI'

  const filteredNavigation = navigation.filter(item => {
    if (item.adminOnly && !isAdmin) return false
    if (item.teacherOnly && isAdmin) return false
    return true
  })

  if (!userInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="flex h-16 items-center gap-4 px-4 md:px-6">
          {/* Mobile menu button */}
          <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-64">
              <MobileNavigation
                navigation={filteredNavigation}
                pathname={pathname}
                onNavigate={() => setSidebarOpen(false)}
              />
            </SheetContent>
          </Sheet>

          {/* Logo */}
          <div className="flex items-center gap-2">
            <GraduationCap className="h-6 w-6 text-blue-600" />
            <span className="font-semibold text-lg">
              {isAdmin ? 'Admin Panel' : 'Teacher Portal'}
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 ml-6">
            {filteredNavigation.map((item) => (
              <Button
                key={item.href}
                variant={pathname === item.href ? "default" : "ghost"}
                size="sm"
                onClick={() => router.push(item.href)}
                className="flex items-center gap-2"
              >
                <item.icon className="h-4 w-4" />
                {item.title}
              </Button>
            ))}
          </nav>

          {/* User menu */}
          <div className="ml-auto">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="" alt={userInfo.tenCanBo} />
                    <AvatarFallback>
                      {userInfo.tenCanBo.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{userInfo.tenCanBo}</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {userInfo.email}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {userInfo.vaiTro} {userInfo.tenKhoa && `- ${userInfo.tenKhoa}`}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => router.push('/profile')}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Thông tin cá nhân</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push(isAdmin ? '/admin/settings' : '/teacher/settings')}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Cài đặt</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Đăng xuất</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 p-4 md:p-6 max-w-7xl mx-auto">
        {children}
      </main>
    </div>
  )
}

function MobileNavigation({
  navigation,
  pathname,
  onNavigate
}: {
  navigation: NavigationItem[]
  pathname: string
  onNavigate: () => void
}) {
  const router = useRouter()

  return (
    <div className="flex flex-col space-y-2 mt-4">
      {navigation.map((item) => (
        <Button
          key={item.href}
          variant={pathname === item.href ? "default" : "ghost"}
          className="justify-start"
          onClick={() => {
            router.push(item.href)
            onNavigate()
          }}
        >
          <item.icon className="mr-2 h-4 w-4" />
          {item.title}
        </Button>
      ))}
    </div>
  )
}
