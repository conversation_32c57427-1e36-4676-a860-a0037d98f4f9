import { ApiClientError } from './apiClient'

// Error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN'
}

// Error handler utility
export class ErrorHandler {
  static getErrorType(error: any): ErrorType {
    if (error instanceof ApiClientError) {
      switch (error.status) {
        case 401:
          return ErrorType.AUTHENTICATION
        case 403:
          return ErrorType.AUTHORIZATION
        case 404:
          return ErrorType.NOT_FOUND
        case 422:
          return ErrorType.VALIDATION
        case 500:
        case 502:
        case 503:
          return ErrorType.SERVER
        default:
          return ErrorType.API
      }
    }
    
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return ErrorType.NETWORK
    }
    
    return ErrorType.UNKNOWN
  }

  static getErrorMessage(error: any): string {
    const errorType = this.getErrorType(error)
    
    switch (errorType) {
      case ErrorType.AUTHENTICATION:
        return 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.'
      case ErrorType.AUTHORIZATION:
        return 'Bạn không có quyền truy cập chức năng này.'
      case ErrorType.NOT_FOUND:
        return 'Không tìm thấy dữ liệu yêu cầu.'
      case ErrorType.VALIDATION:
        return error.response?.message || 'Dữ liệu không hợp lệ.'
      case ErrorType.SERVER:
        return 'Lỗi máy chủ. Vui lòng thử lại sau.'
      case ErrorType.NETWORK:
        return 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.'
      case ErrorType.API:
        return error.message || 'Có lỗi xảy ra khi gọi API.'
      default:
        return 'Có lỗi không xác định xảy ra.'
    }
  }

  static shouldRetry(error: any): boolean {
    const errorType = this.getErrorType(error)
    return errorType === ErrorType.NETWORK || errorType === ErrorType.SERVER
  }

  static shouldRedirectToLogin(error: any): boolean {
    return this.getErrorType(error) === ErrorType.AUTHENTICATION
  }

  static logError(error: any, context?: string): void {
    const errorType = this.getErrorType(error)
    const message = this.getErrorMessage(error)
    
    console.error(`[${errorType}] ${context ? `${context}: ` : ''}${message}`, error)
  }
}

// Hook for handling API errors with fallback data
export function withFallback<T>(
  apiCall: () => Promise<T>,
  fallbackData: T,
  context?: string
): Promise<T> {
  return apiCall().catch((error) => {
    ErrorHandler.logError(error, context)
    
    // If it's a 404 or network error, use fallback data
    const errorType = ErrorHandler.getErrorType(error)
    if (errorType === ErrorType.NOT_FOUND || errorType === ErrorType.NETWORK) {
      console.warn(`Using fallback data for ${context || 'API call'}`)
      return fallbackData
    }
    
    // For other errors, re-throw
    throw error
  })
}

// Utility for handling async operations with error handling
export async function handleAsync<T>(
  operation: () => Promise<T>,
  fallback?: T,
  context?: string
): Promise<{ data: T | null; error: string | null }> {
  try {
    const data = await operation()
    return { data, error: null }
  } catch (error) {
    ErrorHandler.logError(error, context)
    const errorMessage = ErrorHandler.getErrorMessage(error)
    
    return {
      data: fallback || null,
      error: errorMessage
    }
  }
}

export default ErrorHandler
