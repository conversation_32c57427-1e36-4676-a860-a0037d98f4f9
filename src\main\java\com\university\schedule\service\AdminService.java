package com.university.schedule.service;

import java.util.Map;

public interface AdminService {
    
    /**
     * Lấy dữ liệu dashboard quản trị
     */
    Map<String, Object> getDashboardData();
    
    /**
     * L<PERSON><PERSON> thông tin hệ thống
     */
    Map<String, Object> getSystemInfo();
    
    /**
     * L<PERSON>y thống kê hệ thống
     */
    Map<String, Object> getSystemStatistics();
    
    /**
     * Khởi tạo dữ liệu mẫu
     */
    String initializeData();
    
    /**
     * Tạo backup dữ liệu
     */
    String createBackup();
    
    /**
     * Kiểm tra tình trạng hệ thống
     */
    Map<String, Object> checkSystemHealth();
    
    /**
     * Dọn dẹp dữ liệu cũ
     */
    String cleanupOldData();
    
    /**
     * Tối ưu hóa database
     */
    String optimizeDatabase();
}
