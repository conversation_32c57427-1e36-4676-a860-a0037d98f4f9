'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Save, X, BookOpen, Users, Clock, MapPin, User, Calendar } from 'lucide-react'

interface ScheduleFormData {
  // Ngành và lớp
  major: string
  class: string
  studyType: 'LT' | 'TH' | '' // Lý thuyết hoặc Thực hành
  group: string // Chỉ hiện khi chọn TH

  // Môn học và bài học
  subject: string
  lesson: string
  lessonType: 'LT' | 'TH' | ''
  periods: number // Số tiết
  coefficient: number // H<PERSON> số

  // Giảng viên
  department: string
  teacher: string

  // Thời gian
  dayOfWeek: string
  session: 'morning' | 'afternoon' | 'evening' | ''

  // Địa điểm
  campus: 'CS1' | 'CS2' | ''
  areaType: 'LT' | 'TH' | '' // Khu LT hoặc TH
  room: string
}

interface ScheduleFormProps {
  initialData?: Partial<ScheduleFormData>
  timeInfo?: { session?: string; timeSlot?: string }
  onSave: (data: ScheduleFormData) => void
  onCancel: () => void
  isEdit?: boolean
}

export default function ScheduleForm({
  initialData,
  timeInfo,
  onSave,
  onCancel,
  isEdit = false
}: ScheduleFormProps) {
  const [formData, setFormData] = useState<ScheduleFormData>({
    major: '',
    class: '',
    studyType: '',
    group: '',
    subject: '',
    lesson: '',
    lessonType: '',
    periods: 1,
    coefficient: 1,
    department: '',
    teacher: '',
    dayOfWeek: '',
    session: '',
    campus: '',
    areaType: '',
    room: '',
    ...initialData
  })

  const [loading, setLoading] = useState(false)

  // Auto-set time info from calendar click
  useEffect(() => {
    if (timeInfo && !isEdit) {
      setFormData(prev => {
        const updates: Partial<ScheduleFormData> = {}

        // Set session if provided
        if (timeInfo.session) {
          updates.session = timeInfo.session as 'morning' | 'afternoon' | 'evening'
        }

        // Set time slot if provided
        if (timeInfo.timeSlot) {
          const endTime = getEndTimeFromStart(timeInfo.timeSlot)
          updates.periods = calculatePeriods(timeInfo.timeSlot, endTime)
        }

        return { ...prev, ...updates }
      })
    }
  }, [timeInfo, isEdit])

  const getEndTimeFromStart = (startTime: string) => {
    const [hours, minutes] = startTime.split(':').map(Number)
    const endHour = hours + 2 // Default 2-hour duration
    return `${endHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
  }

  const calculatePeriods = (startTime: string, endTime: string) => {
    const [startHour] = startTime.split(':').map(Number)
    const [endHour] = endTime.split(':').map(Number)
    return Math.max(1, endHour - startHour) // At least 1 period
  }

  // Mock data - replace with actual API calls
  const majors = [
    { id: 'cntt', name: 'Công nghệ thông tin' },
    { id: 'ktpm', name: 'Kỹ thuật phần mềm' },
    { id: 'khmt', name: 'Khoa học máy tính' },
    { id: 'attt', name: 'An toàn thông tin' }
  ]

  const getClassesByMajor = (majorId: string) => {
    const classMap: { [key: string]: string[] } = {
      'cntt': ['CNTT01', 'CNTT02', 'CNTT03', 'CNTT04'],
      'ktpm': ['KTPM01', 'KTPM02', 'KTPM03'],
      'khmt': ['KHMT01', 'KHMT02'],
      'attt': ['ATTT01', 'ATTT02']
    }
    return classMap[majorId] || []
  }

  const getSubjectsByMajor = (majorId: string) => {
    const subjectMap: { [key: string]: Array<{id: string, name: string}> } = {
      'cntt': [
        { id: 'java', name: 'Lập trình Java' },
        { id: 'database', name: 'Cơ sở dữ liệu' },
        { id: 'web', name: 'Lập trình Web' },
        { id: 'mobile', name: 'Lập trình Mobile' }
      ],
      'ktpm': [
        { id: 'se', name: 'Công nghệ phần mềm' },
        { id: 'testing', name: 'Kiểm thử phần mềm' },
        { id: 'project', name: 'Quản lý dự án' }
      ]
    }
    return subjectMap[majorId] || []
  }

  const getLessonsBySubject = (subjectId: string) => {
    const lessonMap: { [key: string]: Array<{id: string, name: string, types: ('LT'|'TH')[], defaultType: 'LT'|'TH'}> } = {
      'java': [
        { id: 'java-1', name: 'Bài 1: Giới thiệu Java', types: ['LT'], defaultType: 'LT' },
        { id: 'java-2', name: 'Bài 2: OOP trong Java', types: ['LT', 'TH'], defaultType: 'LT' },
        { id: 'java-3', name: 'Bài 3: Collections Framework', types: ['LT', 'TH'], defaultType: 'TH' },
        { id: 'java-4', name: 'Bài 4: Exception Handling', types: ['LT'], defaultType: 'LT' }
      ],
      'database': [
        { id: 'db-1', name: 'Bài 1: Thiết kế CSDL', types: ['LT'], defaultType: 'LT' },
        { id: 'db-2', name: 'Bài 2: SQL cơ bản', types: ['LT', 'TH'], defaultType: 'TH' },
        { id: 'db-3', name: 'Bài 3: Stored Procedure', types: ['TH'], defaultType: 'TH' }
      ]
    }
    return lessonMap[subjectId] || []
  }

  const departments = [
    { id: 'cntt', name: 'Khoa Công nghệ thông tin' },
    { id: 'dtvt', name: 'Khoa Điện tử viễn thông' },
    { id: 'co-khi', name: 'Khoa Cơ khí' }
  ]

  const getTeachersByDepartment = (deptId: string) => {
    const teacherMap: { [key: string]: Array<{id: string, name: string}> } = {
      'cntt': [
        { id: 'nva', name: 'Nguyễn Văn A' },
        { id: 'ttb', name: 'Trần Thị B' },
        { id: 'lvc', name: 'Lê Văn C' }
      ],
      'dtvt': [
        { id: 'ptd', name: 'Phạm Thị D' },
        { id: 'hve', name: 'Hoàng Văn E' }
      ]
    }
    return teacherMap[deptId] || []
  }

  const daysOfWeek = [
    { id: 'monday', name: 'Thứ 2' },
    { id: 'tuesday', name: 'Thứ 3' },
    { id: 'wednesday', name: 'Thứ 4' },
    { id: 'thursday', name: 'Thứ 5' },
    { id: 'friday', name: 'Thứ 6' },
    { id: 'saturday', name: 'Thứ 7' },
    { id: 'sunday', name: 'Chủ nhật' }
  ]

  const sessions = [
    { id: 'morning', name: 'Buổi sáng', time: '06:00 - 12:00' },
    { id: 'afternoon', name: 'Buổi chiều', time: '12:00 - 18:00' },
    { id: 'evening', name: 'Buổi tối', time: '18:00 - 23:00' }
  ]

  const campuses = [
    { id: 'CS1', name: 'Cơ sở 1' },
    { id: 'CS2', name: 'Cơ sở 2' }
  ]

  const areaTypes = [
    { id: 'LT', name: 'Khu Lý thuyết' },
    { id: 'TH', name: 'Khu Thực hành' }
  ]

  const getRoomsByArea = (campus: string, areaType: string) => {
    const roomMap: { [key: string]: { [key: string]: string[] } } = {
      'CS1': {
        'LT': ['Giảng đường A1', 'Giảng đường A2', 'Giảng đường B1', 'Giảng đường B2'],
        'TH': ['Phòng TH01', 'Phòng TH02', 'Lab01', 'Lab02']
      },
      'CS2': {
        'LT': ['Giảng đường C1', 'Giảng đường C2', 'Giảng đường D1'],
        'TH': ['Phòng TH03', 'Phòng TH04', 'Lab03', 'Lab04']
      }
    }
    return roomMap[campus]?.[areaType] || []
  }

  const groups = ['Nhóm 1', 'Nhóm 2', 'Nhóm 3', 'Nhóm 4', 'Nhóm 5']

  const handleInputChange = (field: keyof ScheduleFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value }

      // Reset dependent fields
      if (field === 'major') {
        newData.class = ''
        newData.subject = ''
        newData.lesson = ''
        newData.lessonType = ''
      }

      if (field === 'studyType' && value !== 'TH') {
        newData.group = ''
      }

      if (field === 'subject') {
        newData.lesson = ''
        newData.lessonType = ''
      }

      if (field === 'lesson') {
        const lessons = getLessonsBySubject(prev.subject)
        const selectedLesson = lessons.find(l => l.id === value)
        if (selectedLesson) {
          newData.lessonType = selectedLesson.defaultType
        }
      }

      if (field === 'department') {
        newData.teacher = ''
      }

      if (field === 'dayOfWeek') {
        newData.session = ''
      }

      if (field === 'campus') {
        newData.areaType = ''
        newData.room = ''
      }

      if (field === 'areaType') {
        newData.room = ''
      }

      return newData
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving schedule:', error)
    } finally {
      setLoading(false)
    }
  }

  const isFormValid = () => {
    return formData.major && formData.class && formData.subject &&
           formData.lesson && formData.teacher && formData.dayOfWeek &&
           formData.session && formData.room && formData.periods > 0
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Thông tin lớp học */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Thông tin lớp học
            </CardTitle>
            <CardDescription>
              Chọn ngành học và lớp học
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="major">Ngành học *</Label>
              <Select value={formData.major} onValueChange={(value) => handleInputChange('major', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn ngành học" />
                </SelectTrigger>
                <SelectContent>
                  {majors.map(major => (
                    <SelectItem key={major.id} value={major.id}>
                      {major.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="class">Lớp học *</Label>
              <Select
                value={formData.class}
                onValueChange={(value) => handleInputChange('class', value)}
                disabled={!formData.major}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn lớp học" />
                </SelectTrigger>
                <SelectContent>
                  {getClassesByMajor(formData.major).map(className => (
                    <SelectItem key={className} value={className}>
                      {className}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="studyType">Hình thức học *</Label>
              <Select value={formData.studyType} onValueChange={(value) => handleInputChange('studyType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn hình thức" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LT">Lý thuyết (LT)</SelectItem>
                  <SelectItem value="TH">Thực hành (TH)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.studyType === 'TH' && (
              <div>
                <Label htmlFor="group">Nhóm học *</Label>
                <Select value={formData.group} onValueChange={(value) => handleInputChange('group', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn nhóm" />
                  </SelectTrigger>
                  <SelectContent>
                    {groups.map(group => (
                      <SelectItem key={group} value={group}>
                        {group}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Thông tin môn học */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Thông tin môn học
            </CardTitle>
            <CardDescription>
              Chọn môn học và bài học
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="subject">Môn học/Mô đun *</Label>
              <Select
                value={formData.subject}
                onValueChange={(value) => handleInputChange('subject', value)}
                disabled={!formData.major}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn môn học" />
                </SelectTrigger>
                <SelectContent>
                  {getSubjectsByMajor(formData.major).map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="lesson">Bài học *</Label>
              <Select
                value={formData.lesson}
                onValueChange={(value) => handleInputChange('lesson', value)}
                disabled={!formData.subject}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn bài học" />
                </SelectTrigger>
                <SelectContent>
                  {getLessonsBySubject(formData.subject).map(lesson => (
                    <SelectItem key={lesson.id} value={lesson.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{lesson.name}</span>
                        <div className="flex gap-1 ml-2">
                          {lesson.types.map(type => (
                            <Badge key={type} variant="outline" className="text-xs">
                              {type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="lessonType">Hình thức *</Label>
                <Select value={formData.lessonType} onValueChange={(value) => handleInputChange('lessonType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="LT/TH" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LT">LT</SelectItem>
                    <SelectItem value="TH">TH</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="periods">Số tiết *</Label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.periods}
                  onChange={(e) => handleInputChange('periods', parseInt(e.target.value) || 1)}
                />
              </div>

              <div>
                <Label htmlFor="coefficient">Hệ số</Label>
                <Input
                  type="number"
                  step="0.1"
                  min="0.1"
                  max="3"
                  value={formData.coefficient}
                  onChange={(e) => handleInputChange('coefficient', parseFloat(e.target.value) || 1)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Thông tin giảng viên */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Thông tin giảng viên
            </CardTitle>
            <CardDescription>
              Chọn khoa và giảng viên
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="department">Khoa *</Label>
              <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn khoa" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map(dept => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="teacher">Giảng viên *</Label>
              <Select
                value={formData.teacher}
                onValueChange={(value) => handleInputChange('teacher', value)}
                disabled={!formData.department}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn giảng viên" />
                </SelectTrigger>
                <SelectContent>
                  {getTeachersByDepartment(formData.department).map(teacher => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Thông tin thời gian */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Thông tin thời gian
            </CardTitle>
            <CardDescription>
              Chọn thứ và buổi học
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="dayOfWeek">Thứ giảng *</Label>
              <Select value={formData.dayOfWeek} onValueChange={(value) => handleInputChange('dayOfWeek', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn thứ" />
                </SelectTrigger>
                <SelectContent>
                  {daysOfWeek.map(day => (
                    <SelectItem key={day.id} value={day.id}>
                      {day.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="session">Buổi học *</Label>
              <Select
                value={formData.session}
                onValueChange={(value) => handleInputChange('session', value)}
                disabled={!formData.dayOfWeek}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn buổi" />
                </SelectTrigger>
                <SelectContent>
                  {sessions.map(session => (
                    <SelectItem key={session.id} value={session.id}>
                      <div className="flex flex-col">
                        <span>{session.name}</span>
                        <span className="text-xs text-gray-500">{session.time}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Thông tin địa điểm */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Thông tin địa điểm
          </CardTitle>
          <CardDescription>
            Chọn cơ sở, khu và phòng học
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <Label htmlFor="campus">Cơ sở *</Label>
              <Select value={formData.campus} onValueChange={(value) => handleInputChange('campus', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn cơ sở" />
                </SelectTrigger>
                <SelectContent>
                  {campuses.map(campus => (
                    <SelectItem key={campus.id} value={campus.id}>
                      {campus.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="areaType">Khu *</Label>
              <Select
                value={formData.areaType}
                onValueChange={(value) => handleInputChange('areaType', value)}
                disabled={!formData.campus}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn khu" />
                </SelectTrigger>
                <SelectContent>
                  {areaTypes.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="room">Phòng học *</Label>
              <Select
                value={formData.room}
                onValueChange={(value) => handleInputChange('room', value)}
                disabled={!formData.campus || !formData.areaType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn phòng" />
                </SelectTrigger>
                <SelectContent>
                  {getRoomsByArea(formData.campus, formData.areaType).map(room => (
                    <SelectItem key={room} value={room}>
                      {room}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit buttons */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Hủy
        </Button>
        <Button type="submit" disabled={!isFormValid() || loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Đang lưu...' : (isEdit ? 'Cập nhật' : 'Tạo lịch')}
        </Button>
      </div>
    </form>
  )
}
