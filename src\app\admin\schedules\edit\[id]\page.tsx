'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import ScheduleForm from '@/components/schedule/ScheduleForm'

export default function EditSchedulePage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [initialData, setInitialData] = useState<any>(null)

  useEffect(() => {
    // Load schedule data
    const loadSchedule = async () => {
      try {
        // Mock API call - replace with actual API
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // Mock data for editing
        const mockData = {
          major: 'cntt',
          class: 'CNTT01',
          studyType: 'LT',
          group: '',
          subject: 'java',
          lesson: 'java-2',
          lessonType: 'LT',
          periods: 2,
          coefficient: 1,
          department: 'cntt',
          teacher: 'nva',
          dayOfWeek: 'monday',
          session: 'morning',
          campus: 'CS1',
          areaType: 'LT',
          room: 'Giảng đường A1'
        }
        
        setInitialData(mockData)
      } catch (error) {
        toast({
          title: "Lỗi",
          description: "Không thể tải thông tin lịch giảng",
          variant: "destructive"
        })
        router.push('/admin/schedules')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      loadSchedule()
    }
  }, [params.id, router, toast])

  const handleSave = async (formData: any) => {
    try {
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Thành công",
        description: "Lịch giảng đã được cập nhật thành công"
      })
      
      router.push('/admin/schedules')
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật lịch giảng",
        variant: "destructive"
      })
      throw error
    }
  }

  const handleCancel = () => {
    router.push('/admin/schedules')
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Đang tải...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/admin/schedules')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Quay lại
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Chỉnh sửa lịch giảng
          </h1>
          <p className="text-muted-foreground">
            Cập nhật thông tin lịch giảng
          </p>
        </div>
      </div>

      {initialData && (
        <ScheduleForm
          initialData={initialData}
          onSave={handleSave}
          onCancel={handleCancel}
          isEdit={true}
        />
      )}
    </div>
  )
}
