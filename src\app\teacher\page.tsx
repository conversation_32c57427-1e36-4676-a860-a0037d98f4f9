"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Calendar,
  Clock,
  FileText,
  Settings,
  LogOut,
  GraduationCap,
  BookOpen,
  BarChart3,
  User,
  TrendingUp,
  ArrowRight
} from "lucide-react"
import StatsCard from '@/components/dashboard/StatsCard'
import QuickActionCard from '@/components/dashboard/QuickActionCard'

interface UserInfo {
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai: string
  vaiTro: string
}

interface TeacherStats {
  totalSchedules: number
  totalHours: number
  thisWeekHours: number
  pendingSchedules: number
}

export default function TeacherDashboard() {
  const [userInfo, setUserInfo] = useState<UserInfo>({
    maCanBo: '',
    tenCanBo: '',
    email: '',
    soDienThoai: '',
    vaiTro: ''
  })
  const [stats, setStats] = useState<TeacherStats | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra token và quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Lấy thông tin user từ localStorage
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr && userInfoStr !== 'undefined') {
      try {
        const user = JSON.parse(userInfoStr)
        setUserInfo(user)

        // Kiểm tra quyền truy cập - chỉ giáo viên mới được vào
        if (user.vaiTro === 'Admin' || user.vaiTro === 'ADMIN' || user.vaiTro === 'QUAN_TRI') {
          router.push('/admin')
          return
        }
      } catch (error) {
        console.error('Error parsing user info:', error)
        localStorage.removeItem('userInfo')
        localStorage.removeItem('token')
        router.push('/login')
        return
      }
    } else {
      router.push('/login')
      return
    }

    // Lấy thống kê giáo viên (mock data)
    setStats({
      totalSchedules: 45,
      totalHours: 120,
      thisWeekHours: 18,
      pendingSchedules: 3
    })
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
  }

  const menuItems = [
    {
      title: 'Quản lý lịch giảng',
      description: 'Tạo và quản lý lịch giảng dạy',
      icon: Calendar,
      href: '/teacher/schedules',
      color: 'bg-blue-500'
    },
    {
      title: 'Giờ giảng dạy',
      description: 'Xem và tính toán giờ giảng',
      icon: Clock,
      href: '/teacher/teaching-hours',
      color: 'bg-green-500'
    },
    {
      title: 'Báo cáo',
      description: 'Xuất báo cáo Excel và PDF',
      icon: FileText,
      href: '/teacher/reports',
      color: 'bg-purple-500'
    },
    {
      title: 'Cài đặt',
      description: 'Thay đổi mật khẩu và thông tin',
      icon: Settings,
      href: '/teacher/settings',
      color: 'bg-gray-500'
    }
  ]

  if (!userInfo.tenCanBo) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-2xl font-bold tracking-tight">
          Chào mừng, {userInfo.tenCanBo}!
        </h1>
        <p className="text-muted-foreground">
          Quản lý lịch giảng và theo dõi giờ giảng dạy của bạn
        </p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Tổng lịch giảng"
            value={stats.totalSchedules}
            description="Trong học kỳ này"
            icon={Calendar}
          />
          <StatsCard
            title="Tổng giờ giảng"
            value={stats.totalHours}
            description="Giờ đã giảng"
            icon={Clock}
          />
          <StatsCard
            title="Tuần này"
            value={stats.thisWeekHours}
            description="Giờ giảng tuần này"
            icon={BarChart3}
          />
          <StatsCard
            title="Chờ xử lý"
            value={stats.pendingSchedules}
            description="Lịch chờ duyệt"
            icon={BookOpen}
          />
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {menuItems.map((item, index) => (
          <QuickActionCard
            key={index}
            title={item.title}
            description={item.description}
            icon={item.icon}
            href={item.href}
            color={item.color}
          />
        ))}
      </div>
    </div>
  )
}
