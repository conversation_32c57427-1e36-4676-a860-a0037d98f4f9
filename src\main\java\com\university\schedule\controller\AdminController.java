package com.university.schedule.controller;

import com.university.schedule.dto.ApiResponse;
import com.university.schedule.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
public class AdminController {

    @Autowired
    private AdminService adminService;

    /**
     * Lấy dữ liệu dashboard quản trị
     */
    @GetMapping("/dashboard")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDashboardData() {
        try {
            Map<String, Object> dashboardData = adminService.getDashboardData();
            return ResponseEntity.ok(ApiResponse.success("Lấy dữ liệu dashboard thành công", dashboardData));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi lấy dữ liệu dashboard: " + e.getMessage()));
        }
    }

    /**
     * L<PERSON><PERSON> thông tin hệ thống
     */
    @GetMapping("/system-info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemInfo() {
        try {
            Map<String, Object> systemInfo = adminService.getSystemInfo();
            return ResponseEntity.ok(ApiResponse.success("Lấy thông tin hệ thống thành công", systemInfo));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi lấy thông tin hệ thống: " + e.getMessage()));
        }
    }

    /**
     * Lấy thống kê hệ thống
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStatistics() {
        try {
            Map<String, Object> statistics = adminService.getSystemStatistics();
            return ResponseEntity.ok(ApiResponse.success("Lấy thống kê hệ thống thành công", statistics));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi lấy thống kê hệ thống: " + e.getMessage()));
        }
    }

    /**
     * Khởi tạo dữ liệu mẫu
     */
    @PostMapping("/initialize")
    public ResponseEntity<ApiResponse<String>> initializeData() {
        try {
            String result = adminService.initializeData();
            return ResponseEntity.ok(ApiResponse.success("Khởi tạo dữ liệu thành công", result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi khởi tạo dữ liệu: " + e.getMessage()));
        }
    }

    /**
     * Tạo backup dữ liệu
     */
    @PostMapping("/backup")
    public ResponseEntity<ApiResponse<String>> createBackup() {
        try {
            String backupPath = adminService.createBackup();
            return ResponseEntity.ok(ApiResponse.success("Tạo backup thành công", backupPath));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi tạo backup: " + e.getMessage()));
        }
    }

    /**
     * Kiểm tra tình trạng hệ thống
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkSystemHealth() {
        try {
            Map<String, Object> health = adminService.checkSystemHealth();
            return ResponseEntity.ok(ApiResponse.success("Kiểm tra hệ thống thành công", health));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi kiểm tra hệ thống: " + e.getMessage()));
        }
    }

    /**
     * Dọn dẹp dữ liệu cũ
     */
    @PostMapping("/cleanup")
    public ResponseEntity<ApiResponse<String>> cleanupOldData() {
        try {
            String result = adminService.cleanupOldData();
            return ResponseEntity.ok(ApiResponse.success("Dọn dẹp dữ liệu thành công", result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi dọn dẹp dữ liệu: " + e.getMessage()));
        }
    }

    /**
     * Tối ưu hóa database
     */
    @PostMapping("/optimize")
    public ResponseEntity<ApiResponse<String>> optimizeDatabase() {
        try {
            String result = adminService.optimizeDatabase();
            return ResponseEntity.ok(ApiResponse.success("Tối ưu hóa database thành công", result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("Lỗi khi tối ưu hóa database: " + e.getMessage()));
        }
    }
}
