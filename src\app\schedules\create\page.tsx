'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowLeft,
  Save,
  Calendar,
  Clock,
  MapPin,
  User,
  BookOpen,
  Users
} from 'lucide-react'

export default function CreateSchedulePage() {
  const [formData, setFormData] = useState({
    subject: '',
    class: '',
    teacher: '',
    room: '',
    session: '',
    format: '',
    startDate: '',
    endDate: '',
    hours: 0,
    notes: ''
  })

  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error'>('success')
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    // Validation
    if (!formData.subject || !formData.class || !formData.teacher || !formData.room) {
      setMessage('Vui lòng điền đầy đủ thông tin bắt buộc')
      setMessageType('error')
      setLoading(false)
      return
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setMessage('Tạo lịch giảng thành công!')
      setMessageType('success')
      
      setTimeout(() => {
        router.push('/schedules')
      }, 2000)
    } catch (error) {
      setMessage('Có lỗi xảy ra khi tạo lịch giảng')
      setMessageType('error')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: name === 'hours' ? parseInt(value) || 0 : value
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => router.push('/schedules')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <Calendar className="h-6 w-6 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Tạo lịch giảng mới
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {message && (
          <Alert variant={messageType === 'error' ? 'destructive' : 'default'} className="mb-6">
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Thông tin lịch giảng</CardTitle>
            <CardDescription>
              Điền đầy đủ thông tin để tạo lịch giảng mới
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Row 1: Môn học và Lớp */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="subject" className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Môn học <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    placeholder="Nhập tên môn học"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="class" className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Lớp <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="class"
                    name="class"
                    value={formData.class}
                    onChange={handleInputChange}
                    placeholder="Nhập tên lớp"
                    required
                  />
                </div>
              </div>

              {/* Row 2: Giảng viên và Phòng học */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="teacher" className="flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    Giảng viên <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="teacher"
                    name="teacher"
                    value={formData.teacher}
                    onChange={handleInputChange}
                    placeholder="Nhập tên giảng viên"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="room" className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Phòng học <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="room"
                    name="room"
                    value={formData.room}
                    onChange={handleInputChange}
                    placeholder="Nhập phòng học"
                    required
                  />
                </div>
              </div>

              {/* Row 3: Buổi học và Hình thức */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="session" className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Buổi học
                  </Label>
                  <select
                    id="session"
                    name="session"
                    value={formData.session}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn buổi học</option>
                    <option value="morning">Sáng (7:00-11:00)</option>
                    <option value="afternoon">Chiều (13:00-17:00)</option>
                    <option value="evening">Tối (18:00-21:00)</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="format">Hình thức</Label>
                  <select
                    id="format"
                    name="format"
                    value={formData.format}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn hình thức</option>
                    <option value="theory">Lý thuyết</option>
                    <option value="practice">Thực hành</option>
                    <option value="discussion">Thảo luận</option>
                  </select>
                </div>
              </div>

              {/* Row 4: Thời gian và Số tiết */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Ngày bắt đầu</Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">Ngày kết thúc</Label>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="hours">Số tiết</Label>
                  <Input
                    id="hours"
                    name="hours"
                    type="number"
                    min="1"
                    value={formData.hours || ''}
                    onChange={handleInputChange}
                    placeholder="Nhập số tiết"
                  />
                </div>
              </div>

              {/* Row 5: Ghi chú */}
              <div className="space-y-2">
                <Label htmlFor="notes">Ghi chú</Label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nhập ghi chú (tùy chọn)"
                />
              </div>

              {/* Preview */}
              {formData.subject && formData.class && (
                <Card className="bg-blue-50 border-blue-200">
                  <CardHeader>
                    <CardTitle className="text-lg text-blue-800">Xem trước lịch giảng</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div><strong>Môn học:</strong> {formData.subject}</div>
                      <div><strong>Lớp:</strong> {formData.class}</div>
                      <div><strong>Giảng viên:</strong> {formData.teacher}</div>
                      <div><strong>Phòng học:</strong> {formData.room}</div>
                      {formData.session && <div><strong>Buổi học:</strong> {formData.session}</div>}
                      {formData.format && <div><strong>Hình thức:</strong> {formData.format}</div>}
                      {formData.startDate && <div><strong>Từ ngày:</strong> {new Date(formData.startDate).toLocaleDateString('vi-VN')}</div>}
                      {formData.endDate && <div><strong>Đến ngày:</strong> {new Date(formData.endDate).toLocaleDateString('vi-VN')}</div>}
                      {formData.hours > 0 && <div><strong>Số tiết:</strong> {formData.hours}</div>}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/schedules')}
                >
                  Hủy
                </Button>
                <Button type="submit" disabled={loading}>
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? 'Đang tạo...' : 'Tạo lịch giảng'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
