'use client'

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Calendar, X } from 'lucide-react'
import ScheduleForm from '@/components/schedule/ScheduleForm'

interface QuickCreateModalProps {
  isOpen: boolean
  onClose: () => void
  selectedDate: string | null
  timeInfo?: { session?: string; timeSlot?: string }
  onSave: (scheduleData: any) => void
}

export default function QuickCreateModal({
  isOpen,
  onClose,
  selectedDate,
  timeInfo,
  onSave
}: QuickCreateModalProps) {

  const handleSave = async (formData: any) => {
    try {
      await onSave({
        ...formData,
        date: selectedDate,
        status: 'pending'
      })
      onClose()
    } catch (error) {
      console.error('Error creating schedule:', error)
      throw error
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Tạo lịch giảng nhanh
              </DialogTitle>
              <DialogDescription>
                {selectedDate && formatDate(selectedDate)}
              </DialogDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <ScheduleForm
          selectedDate={selectedDate || undefined}
          timeInfo={timeInfo}
          onSave={handleSave}
          onCancel={onClose}
          isEdit={false}
        />
      </DialogContent>
    </Dialog>
  )
}
