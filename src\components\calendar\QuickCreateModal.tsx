'use client'

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Calendar, Save, X } from 'lucide-react'

interface QuickCreateModalProps {
  isOpen: boolean
  onClose: () => void
  selectedDate: string | null
  onSave: (scheduleData: any) => void
}

interface QuickScheduleData {
  subject: string
  class: string
  teacher: string
  room: string
  startTime: string
  endTime: string
  notes: string
}

export default function QuickCreateModal({
  isOpen,
  onClose,
  selectedDate,
  onSave
}: QuickCreateModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<QuickScheduleData>({
    subject: '',
    class: '',
    teacher: '',
    room: '',
    startTime: '',
    endTime: '',
    notes: ''
  })

  const handleInputChange = (field: keyof QuickScheduleData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.subject || !formData.class || !formData.teacher || !formData.room || 
        !formData.startTime || !formData.endTime) {
      alert('Vui lòng điền đầy đủ thông tin bắt buộc')
      return
    }

    setLoading(true)
    try {
      await onSave({
        ...formData,
        date: selectedDate,
        status: 'pending'
      })
      
      // Reset form
      setFormData({
        subject: '',
        class: '',
        teacher: '',
        room: '',
        startTime: '',
        endTime: '',
        notes: ''
      })
      
      onClose()
    } catch (error) {
      console.error('Error creating schedule:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Tạo lịch giảng nhanh
              </DialogTitle>
              <DialogDescription>
                {selectedDate && formatDate(selectedDate)}
              </DialogDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="subject">Môn học *</Label>
            <Select value={formData.subject} onValueChange={(value) => handleInputChange('subject', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn môn học" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="lap-trinh-java">Lập trình Java</SelectItem>
                <SelectItem value="co-so-du-lieu">Cơ sở dữ liệu</SelectItem>
                <SelectItem value="mang-may-tinh">Mạng máy tính</SelectItem>
                <SelectItem value="he-dieu-hanh">Hệ điều hành</SelectItem>
                <SelectItem value="phan-tich-thiet-ke">Phân tích thiết kế hệ thống</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="class">Lớp học *</Label>
            <Select value={formData.class} onValueChange={(value) => handleInputChange('class', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn lớp học" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CNTT01">CNTT01</SelectItem>
                <SelectItem value="CNTT02">CNTT02</SelectItem>
                <SelectItem value="CNTT03">CNTT03</SelectItem>
                <SelectItem value="CNTT04">CNTT04</SelectItem>
                <SelectItem value="KTPM01">KTPM01</SelectItem>
                <SelectItem value="KTPM02">KTPM02</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="teacher">Giảng viên *</Label>
            <Select value={formData.teacher} onValueChange={(value) => handleInputChange('teacher', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn giảng viên" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="nguyen-van-a">Nguyễn Văn A</SelectItem>
                <SelectItem value="tran-thi-b">Trần Thị B</SelectItem>
                <SelectItem value="le-van-c">Lê Văn C</SelectItem>
                <SelectItem value="pham-thi-d">Phạm Thị D</SelectItem>
                <SelectItem value="hoang-van-e">Hoàng Văn E</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="room">Phòng học *</Label>
            <Select value={formData.room} onValueChange={(value) => handleInputChange('room', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn phòng học" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="P101">P101</SelectItem>
                <SelectItem value="P102">P102</SelectItem>
                <SelectItem value="P103">P103</SelectItem>
                <SelectItem value="P201">P201</SelectItem>
                <SelectItem value="P202">P202</SelectItem>
                <SelectItem value="LAB01">LAB01</SelectItem>
                <SelectItem value="LAB02">LAB02</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startTime">Giờ bắt đầu *</Label>
              <Input
                id="startTime"
                type="time"
                value={formData.startTime}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="endTime">Giờ kết thúc *</Label>
              <Input
                id="endTime"
                type="time"
                value={formData.endTime}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Ghi chú</Label>
            <Textarea
              id="notes"
              placeholder="Ghi chú thêm..."
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={2}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Đang tạo...' : 'Tạo lịch'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
