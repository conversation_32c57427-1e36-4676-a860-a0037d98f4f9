"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  ArrowLeft,
  Save,
  Calendar,
  Clock
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface ScheduleForm {
  subject: string
  class: string
  room: string
  dayOfWeek: string
  startTime: string
  endTime: string
  startDate: string
  endDate: string
}

export default function CreateSchedule() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<ScheduleForm>({
    subject: '',
    class: '',
    room: '',
    dayOfWeek: '',
    startTime: '',
    endTime: '',
    startDate: '',
    endDate: ''
  })

  const handleInputChange = (field: keyof ScheduleForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!formData.subject || !formData.class || !formData.room || 
        !formData.dayOfWeek || !formData.startTime || !formData.endTime ||
        !formData.startDate || !formData.endDate) {
      toast({
        title: "Lỗi",
        description: "Vui lòng điền đầy đủ thông tin",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Thành công",
        description: "Lịch giảng đã được tạo thành công"
      })
      
      router.push('/teacher/schedules')
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tạo lịch giảng",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/teacher/schedules')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Quay lại
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tạo Lịch Giảng Mới</h1>
          <p className="text-muted-foreground">
            Tạo lịch giảng dạy cho môn học của bạn
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Thông tin lịch giảng
          </CardTitle>
          <CardDescription>
            Điền đầy đủ thông tin để tạo lịch giảng mới
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Subject */}
              <div className="space-y-2">
                <Label htmlFor="subject">Môn học *</Label>
                <Select onValueChange={(value) => handleInputChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn môn học" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="java">Lập trình Java</SelectItem>
                    <SelectItem value="database">Cơ sở dữ liệu</SelectItem>
                    <SelectItem value="network">Mạng máy tính</SelectItem>
                    <SelectItem value="web">Lập trình Web</SelectItem>
                    <SelectItem value="mobile">Lập trình Mobile</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Class */}
              <div className="space-y-2">
                <Label htmlFor="class">Lớp học *</Label>
                <Select onValueChange={(value) => handleInputChange('class', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn lớp học" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CNTT01">CNTT01</SelectItem>
                    <SelectItem value="CNTT02">CNTT02</SelectItem>
                    <SelectItem value="CNTT03">CNTT03</SelectItem>
                    <SelectItem value="KTPM01">KTPM01</SelectItem>
                    <SelectItem value="KTPM02">KTPM02</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Room */}
              <div className="space-y-2">
                <Label htmlFor="room">Phòng học *</Label>
                <Select onValueChange={(value) => handleInputChange('room', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn phòng học" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="A101">A101</SelectItem>
                    <SelectItem value="A102">A102</SelectItem>
                    <SelectItem value="B201">B201</SelectItem>
                    <SelectItem value="B202">B202</SelectItem>
                    <SelectItem value="C301">C301</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Day of Week */}
              <div className="space-y-2">
                <Label htmlFor="dayOfWeek">Thứ trong tuần *</Label>
                <Select onValueChange={(value) => handleInputChange('dayOfWeek', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn thứ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monday">Thứ 2</SelectItem>
                    <SelectItem value="tuesday">Thứ 3</SelectItem>
                    <SelectItem value="wednesday">Thứ 4</SelectItem>
                    <SelectItem value="thursday">Thứ 5</SelectItem>
                    <SelectItem value="friday">Thứ 6</SelectItem>
                    <SelectItem value="saturday">Thứ 7</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Start Time */}
              <div className="space-y-2">
                <Label htmlFor="startTime">Giờ bắt đầu *</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => handleInputChange('startTime', e.target.value)}
                />
              </div>

              {/* End Time */}
              <div className="space-y-2">
                <Label htmlFor="endTime">Giờ kết thúc *</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => handleInputChange('endTime', e.target.value)}
                />
              </div>

              {/* Start Date */}
              <div className="space-y-2">
                <Label htmlFor="startDate">Ngày bắt đầu *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                />
              </div>

              {/* End Date */}
              <div className="space-y-2">
                <Label htmlFor="endDate">Ngày kết thúc *</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                />
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/teacher/schedules')}
              >
                Hủy
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Đang tạo...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Tạo lịch giảng
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </main>
  )
}
