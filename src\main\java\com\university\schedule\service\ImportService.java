package com.university.schedule.service;

import org.springframework.web.multipart.MultipartFile;
import java.util.Map;

public interface ImportService {
    
    /**
     * Đồng bộ dữ liệu từ hệ thống nguồn
     */
    Map<String, Object> syncDataFromSource(String type) throws Exception;
    
    /**
     * Import dữ liệu từ file upload
     */
    Map<String, Object> importFromFile(MultipartFile file, String type) throws Exception;
    
    /**
     * Lấy trạng thái import của tất cả loại dữ liệu
     */
    Map<String, Object> getImportStatus();
    
    /**
     * <PERSON><PERSON><PERSON> tất cả dữ liệu của một loại
     */
    void clearData(String type) throws Exception;
    
    /**
     * Kiểm tra kết nối với hệ thống nguồn
     */
    boolean testSourceConnection();
}
